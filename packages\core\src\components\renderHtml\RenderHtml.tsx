'use client'

import { useEffect, useRef } from 'react'

import './style.css'

/**
 * 渲染 HTML（全站统一隐藏 404 图片）
 * @param {object} props - 组件参数
 * @param {string} props.content - HTML 内容
 * @param {number} props.width - 宽度
 * @param {number} props.center - 居中
 */
const RenderHtml = ({
  content,
  width,
  center,
  contentStyle = '',
}: {
  content: string
  width?: number
  center?: boolean
  contentStyle?: string
}) => {
  const containerRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const el = containerRef.current
    if (!el) return

    const hide = (img: HTMLImageElement) => {
      img.style.display = 'none'
    }

    const onErr = (e: Event) => hide(e.target as HTMLImageElement)

    const watch = (img: HTMLImageElement) => {
      img.addEventListener('error', onErr, { passive: true })
      if (img.complete) {
        if (img.naturalWidth === 0) hide(img)
      } else {
        const onLoad = () => {
          if (img.naturalWidth === 0) hide(img)
        }
        img.addEventListener('load', onLoad, { passive: true, once: true })
      }
    }

    const scan = () => {
      el.querySelectorAll('img').forEach((img) => watch(img as HTMLImageElement))
    }

    scan()

    const mo = new MutationObserver(() => scan())
    mo.observe(el, { childList: true, subtree: true })

    return () => {
      el.querySelectorAll('img').forEach((img) => img.removeEventListener('error', onErr))
      mo.disconnect()
    }
  }, [content])

  return (
    <div
      ref={containerRef}
      className={`${width ? `w-[${width}px]` : 'w-full'} ${center ? 'flex flex-col items-center' : ''} render-html-container ${contentStyle}`}
      dangerouslySetInnerHTML={{ __html: content }}
    />
  )
}

export default RenderHtml

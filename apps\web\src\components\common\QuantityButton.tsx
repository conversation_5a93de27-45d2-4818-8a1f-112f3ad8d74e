'use client'
import { useEffect, useRef } from 'react'
import { Button, InputNumber } from 'antd'

import { Minus, Plus } from '@/components'

interface QuantitySelectorProps {
  quantity: number
  onQuantityChange: (quantity: number, oldValue: number) => void
  maxQuantity: number
  minQuantity?: number
  small?: boolean
  isIncrementDisabled?: boolean
  /**
   * 忽略最大值限制，允许点击加号但由父组件处理限制逻辑
   * 如果为 true，点击加号按钮时不会在组件内部阻止超过 maxQuantity
   * 默认为 false，保持向后兼容的行为
   */
  ignoreMaxLimit?: boolean
}

const QuantityButton = ({
  quantity,
  onQuantityChange,
  maxQuantity = 99,
  minQuantity = 1,
  small = false,
  isIncrementDisabled = false,
  ignoreMaxLimit = false,
}: QuantitySelectorProps) => {
  const inputRef = useRef<HTMLInputElement>(null)

  // 检测是否为iPad设备
  const isIPad = () => {
    const userAgent = navigator.userAgent
    return /iPad/.test(userAgent) || (/Macintosh/.test(userAgent) && navigator.maxTouchPoints > 1)
  }

  // 检测是否为横屏模式
  const isLandscape = () => {
    return (
      window.orientation === 90 ||
      window.orientation === -90 ||
      window.innerWidth > window.innerHeight
    )
  }

  // 键盘避让处理函数
  const handleInputFocus = () => {
    // 只在iPad横屏模式下进行键盘避让
    if (!isIPad() || !isLandscape()) {
      return
    }

    // 延迟执行，等待键盘完全弹起和布局稳定
    setTimeout(() => {
      if (inputRef.current) {
        try {
          // 获取输入框的边界信息
          const inputRect = inputRef.current.getBoundingClientRect()
          const currentScrollTop = window.pageYOffset || document.documentElement.scrollTop

          // 获取当前有效视口高度（键盘弹起后）
          const viewportHeight = window.visualViewport?.height || window.innerHeight

          // 计算输入框底部位置
          const inputBottom = inputRect.bottom
          // 为iPad横屏添加额外的安全边距，确保输入框完全可见
          const safetyMargin = 80
          const effectiveViewportBottom = viewportHeight - safetyMargin

          // 如果输入框被遮挡或太接近底部
          if (inputBottom > effectiveViewportBottom) {
            // 计算需要滚动的距离
            const scrollDistance = inputBottom - effectiveViewportBottom + 40
            const targetScrollTop = currentScrollTop + scrollDistance

            // 平滑滚动到目标位置
            window.scrollTo({
              top: targetScrollTop,
              behavior: 'smooth',
            })
          }
        } catch (error) {
          console.warn('键盘避让处理失败:', error)
        }
      }
    }, 600) // iPad需要更长的延迟时间等待布局完全稳定
  }

  // 监听Visual Viewport API变化（更精确的键盘检测）
  useEffect(() => {
    const handleViewportChange = () => {
      if (document.activeElement === inputRef.current && isIPad() && isLandscape()) {
        handleInputFocus()
      }
    }

    if (window.visualViewport) {
      window.visualViewport.addEventListener('resize', handleViewportChange)
      return () => {
        window.visualViewport?.removeEventListener('resize', handleViewportChange)
      }
    }
    //eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])
  const handleQuantityChange = (step: number) => {
    const newValue = quantity + step

    if (step < 0) {
      // 减少数量时的逻辑
      if (quantity > maxQuantity) {
        // 如果当前数量超过最大值，点击减号直接设置为最大值
        onQuantityChange(maxQuantity, quantity)
      } else if (newValue >= minQuantity) {
        // 正常减少逻辑
        onQuantityChange(newValue, quantity)
      }
    } else {
      // 增加数量时，根据 ignoreMaxLimit 决定行为
      if (ignoreMaxLimit) {
        // 忽略最大值限制，直接调用 onQuantityChange
        onQuantityChange(newValue, quantity)
      } else {
        // 保持原有行为，仅在不超过最大值时调用
        if (newValue <= maxQuantity) {
          onQuantityChange(newValue, quantity)
        }
      }
    }
  }

  const inputQuantityChange = (value: number) => {
    // 将小数四舍五入，确保数量为整数
    const newValue = Math.round(value)
    if (newValue >= maxQuantity) {
      onQuantityChange(maxQuantity, quantity)
    } else if (newValue <= minQuantity) {
      onQuantityChange(minQuantity, quantity)
    } else {
      onQuantityChange(newValue, quantity)
    }
  }

  return (
    <div className={`number-up-down-field ${small ? 'small' : ''}`}>
      <Button
        className="change-btn"
        icon={<Minus size={small ? 14 : 16} />}
        type="text"
        disabled={quantity <= minQuantity || isIncrementDisabled}
        onClick={() => handleQuantityChange(-1)}
      />
      <InputNumber
        ref={inputRef}
        min={minQuantity}
        max={maxQuantity}
        value={quantity}
        onChange={(value) => inputQuantityChange(value ?? minQuantity)}
        onFocus={handleInputFocus}
        controls={false}
        variant="borderless"
        className="quantity-input"
        disabled={isIncrementDisabled}
      />
      <Button
        className="change-btn"
        icon={<Plus size={small ? 14 : 16} />}
        type="text"
        disabled={(ignoreMaxLimit ? false : quantity >= maxQuantity) || isIncrementDisabled}
        onClick={() => handleQuantityChange(1)}
      />
    </div>
  )
}

export default QuantityButton

'use client'
import { Fragment, useState } from 'react'
import Image from 'next/image'
import { IconStoreTag, RenderHtml, useGetCmsBlockQuery } from '@ninebot/core'

import { HeartOutlined, IconArrow, Modal, Skeleton } from '@/components'
import { ProductDetails } from '@/types/product'
interface ServicePolicyProps {
  data: {
    label: string
    value: string
  }[]
  product: ProductDetails
  deliveryMethodPickup: boolean
}

const ServicePolicy = ({ data, product, deliveryMethodPickup }: ServicePolicyProps) => {
  const [isModalOpen, setIsModalOpen] = useState(false)

  // 处理弹窗打开/关闭时的背景滚动控制
  const handleModalToggle = (open: boolean) => {
    setIsModalOpen(open)

    // 在移动设备上防止背景滚动
    if (typeof document !== 'undefined') {
      if (open) {
        document.body.style.overflow = 'hidden'
        document.body.style.touchAction = 'none'
      } else {
        document.body.style.overflow = ''
        document.body.style.touchAction = ''
      }
    }
  }

  const safeguardIds = data.map((item: { label: string; value: string }) => item.value)
  const { currentData: safeguardData, isFetching } = useGetCmsBlockQuery({
    identifiers: safeguardIds,
  })

  const safeguardContent = safeguardData ? safeguardData?.cmsBlocks?.items : []

  if (isFetching) {
    return <Skeleton style={{ width: '100%', height: '48px' }} />
  }

  if (safeguardContent?.length === 0) {
    return null
  }

  return (
    <>
      <div
        className="flex cursor-pointer items-center justify-between rounded-base bg-gray-4 px-base-16 py-base-12"
        onClick={() => handleModalToggle(true)}>
        <span className="flex-none">
          <HeartOutlined />
        </span>
        <div className="ml-[8px] line-clamp-1 h-[24px] flex-1 space-x-[12px]">
          {data.map((item, index) => (
            <Fragment key={index}>
              <span className="font-miSansRegular330 text-[12px] leading-none">{item.label}</span>
              {index !== data.length - 1 && (
                <span className="-mt-[1px] inline-block h-[4px] w-[4px] bg-black align-middle"></span>
              )}
            </Fragment>
          ))}
        </div>
        <span className="flex-none">
          <IconArrow size={20} rotate={-90} color="#86868B" />
        </span>
      </div>

      <Modal
        title="服务保障"
        isOpen={isModalOpen}
        onClose={() => handleModalToggle(false)}
        width={480}
        footer={null}>
        <div className="-mr-base-12 max-h-[406px] overflow-y-auto pr-base-12 [overscroll-behavior:contain] [touch-action:pan-y]">
          <div className="mb-base-16 flex items-center gap-base-16">
            <Image
              src={product.image.url}
              alt="商品图片"
              width={60}
              height={60}
              className="rounded-base bg-[#F8F8F9] object-cover"
            />

            <div className="line-clamp-2 text-[16px] leading-[1.4]">
              <span>{deliveryMethodPickup && <IconStoreTag className="mb-1" />}</span>
              <span>{product?.name}</span>
            </div>
          </div>
          <div className="flex flex-col gap-base-12">
            {safeguardContent?.map((item, index) => (
              <div key={index} className="rounded-base-12 bg-[#F8F8F9] p-base-16">
                <div className="font-miSansMedium380 text-lg leading-[1.4] text-black">
                  {item?.title}
                </div>
                {item?.content ? (
                  <div className="mt-[8px] font-miSansRegular330 text-base leading-[1.4] text-[#6E6E73]">
                    <RenderHtml content={item?.content} />
                  </div>
                ) : null}
              </div>
            ))}
          </div>
        </div>
      </Modal>
    </>
  )
}

export default ServicePolicy

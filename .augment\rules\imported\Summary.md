---
type: "always_apply"
---

# SUMMARY.md

This file provides guidance when working with code in this repository.

## Rules

- Alaways respond in Chinese.

## Project Architecture

This is a **Turborepo monorepo** with two Next.js applications:

- **Web app** (`apps/web/`) - Desktop application using Ant Design
- **H5 app** (`apps/h5/`) - Mobile application using Ant Design Mobile (runs on port 3001)

**Tech Stack:**

- Next.js 14 with App Router
- TypeScript
- React 18
- Tailwind CSS
- GraphQL with code generation
- Redux Toolkit for state management
- International support via next-intl
- Redis for caching

**Monorepo Structure:**

- `apps/web/` - Desktop Next.js app
- `apps/h5/` - Mobile Next.js app
- `packages/core/` - Shared core functionality
- `packages/eslint-config/` - Shared ESLint configuration
- `packages/tailwind-config/` - Shared Tailwind configuration

## Development Commands

**Root level commands (affects all apps):**

```bash
pnpm dev           # Start all apps in development
pnpm build         # Build all apps
pnpm lint          # Lint all apps
pnpm type-check    # Type check all apps
pnpm clean         # Clean build outputs
pnpm format        # Format code with Prettier
pnpm clean:cache   # Clean Turbo cache
```

**Individual app commands:**

```bash
# Navigate to specific app first (cd apps/web or cd apps/h5)
pnpm dev           # Start single app (web on 3000, h5 on 3001)
pnpm build         # Build single app
pnpm lint          # Lint single app
pnpm type-check    # Type check single app

# GraphQL code generation
pnpm codegen:dev   # Generate GraphQL types from dev environment
pnpm codegen:prod  # Generate GraphQL types from prod environment
```

**Production deployment:**

```bash
pnpm deploy:master # Deploy as master instance with PM2
pnpm deploy:slave  # Deploy as slave instance with PM2
```

## Code Organization

**Web App Structure (`apps/web/src/`):**

- `app/` - Next.js App Router pages with internationalization
- `components/` - Reusable UI components
- `businessComponents/` - Business-specific components
- `hooks/` - Custom React hooks
- `store/` - Redux store and slices
- `services/` - API services and GraphQL queries
- `utils/` - Utility functions
- `types/` - TypeScript type definitions
- `styles/` - Global styles and theme configuration
- `i18n/` - Internationalization configuration

**Key Points:**

- Both apps share packages from `packages/core`
- Use workspace protocol (`workspace:*`) for internal dependencies
- GraphQL queries require code generation before building
- Mobile app uses `antd-mobile` while web app uses `antd`
- All apps support internationalization via next-intl

## Development Guidelines

- **Package Manager:** Use `pnpm` (version >= 9.7.1)
- **Node Version:** >= 20.16.0 (check `.nvmrc`)
- **Commit Convention:** Follows conventional commits (enforced by commitlint)
- **Pre-commit Hooks:** ESLint, Prettier, and Stylelint via lint-staged
- **Type Checking:** Run `pnpm type-check` before committing
- **GraphQL:** Regenerate types with `pnpm codegen:dev` when schema changes

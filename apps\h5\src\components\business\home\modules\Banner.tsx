'use client'

import { useMemo, useRef, useState } from 'react'
import {
  generateBannerEventParams,
  RelationItem,
  TRACK_EVENT,
  useVolcAnalytics,
} from '@ninebot/core'
import { Image } from 'antd-mobile'
import { Swiper } from 'antd-mobile'
import type { SwiperRef } from 'antd-mobile/es/components/swiper'
import clsx from 'clsx'

import { Video } from '@/businessComponents'
import { VideoRef } from '@/businessComponents/Video'
import { Arrow } from '@/components/icons'
import { Link } from '@/i18n/navigation'

type BannerProps = {
  bannerData: RelationItem[]
  isHome?: boolean
  containerClassName?: string
}

const Banner = ({ bannerData, isHome = false, containerClassName = '' }: BannerProps) => {
  const swiperRef = useRef<SwiperRef>(null)
  const videoRefs = useRef<Record<number, VideoRef | null>>({})
  const [activeIndex, setActiveIndex] = useState(0)
  const { reportEvent } = useVolcAnalytics()

  const bannerDataFiltered = useMemo(() => {
    return bannerData?.filter((item) => item?.button_url?.type !== 'seckill')
  }, [bannerData])

  /**
   * 是否展示全屏
   */
  const showFullScreen = useMemo(
    () => bannerDataFiltered?.some((item) => item?.fullscreen),
    [bannerDataFiltered],
  )

  /**
   * 宽高比例
   */
  const imageRatio = useMemo(() => {
    const item = bannerDataFiltered?.find((ite) => ite?.image_ratio_mobile)
    if (item?.image_ratio_mobile) {
      return `${item?.image_ratio_mobile.width}/${item?.image_ratio_mobile.height}`
    }
    return '1/1'
  }, [bannerDataFiltered])

  // 处理轮播切换
  const handleChange = (index: number) => {
    // 暂停当前播放的视频
    const currentVideoRef = videoRefs.current[activeIndex]
    if (currentVideoRef && bannerDataFiltered[activeIndex]?.video_url) {
      currentVideoRef.pause()
    }

    setActiveIndex(index)

    // 如果新切换到的是视频，自动播放
    const newVideoRef = videoRefs.current[index]
    if (newVideoRef && bannerDataFiltered[index]?.video_url) {
      newVideoRef.play()
    }
  }

  // 处理视频点击
  const handleVideoClick = () => {
    // Video 组件内部会处理播放/暂停逻辑
  }

  // 处理banner点击事件
  const handleBannerClick = (item: RelationItem) => {
    if (isHome) {
      // 上报banner图片曝光事件
      reportEvent(
        TRACK_EVENT.shop_homepage_banner_picture_exposure,
        generateBannerEventParams({
          id: item?.id,
          title: item?.title,
        }),
      )

      // 上报banner图片点击事件
      reportEvent(
        TRACK_EVENT.shop_homepage_banner_picture_click,
        generateBannerEventParams({
          id: item?.id,
          title: item?.title,
        }),
      )
    }
  }

  // 处理查看详情按钮点击事件
  const handleDetailButtonClick = (item: RelationItem, e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()

    if (isHome) {
      reportEvent(
        TRACK_EVENT.shop_homepage_top_banner_detail_button_click,
        generateBannerEventParams({
          id: item?.id,
          title: item?.title,
        }),
      )
    }

    // 处理页面跳转
    if (item?.button_url?.url) {
      window.open(item.button_url.url, '_self')
    }
  }

  return (
    <div className={clsx('relative h-full w-full overflow-hidden', containerClassName)}>
      <Swiper
        ref={swiperRef}
        loop
        autoplay={true}
        autoplayInterval={5000}
        indicatorProps={{
          style: {
            display: bannerDataFiltered.length > 1 ? 'flex' : 'none',
            '--dot-color': isHome ? '#a9a9a9' : '#FFFFFF73',
            '--active-dot-color': isHome ? '#e7e7e7' : '#fff',
            '--dot-size': isHome ? '6px' : '8px',
            '--active-dot-size': isHome ? '40px' : '24px',
            '--dot-border-radius': '50%',
            '--active-dot-border-radius': '12px',
            '--dot-spacing': '8px',
          },
        }}
        onIndexChange={handleChange}
        className="home-swiper h-full">
        {bannerDataFiltered.map((item, index) => (
          <Swiper.Item key={item?.id}>
            <div
              className="relative h-full w-full"
              style={{
                height: showFullScreen ? '100vh' : 'auto',
                aspectRatio: showFullScreen ? '1/1' : `${imageRatio}`,
              }}>
              {item?.video_url ? (
                <>
                  {/* 视频层 - 不包裹在Link中 */}
                  <div className="absolute inset-0">
                    <Video
                      ref={(ref: VideoRef | null) => {
                        const itemIndex = bannerDataFiltered.findIndex(
                          (banner) => banner?.id === item?.id,
                        )
                        videoRefs.current[itemIndex] = ref
                      }}
                      video_url={item.video_url}
                      poster={item.image_url || ''}
                      onClick={handleVideoClick}
                      withMute={true}
                      initialPaused={false}
                      initialMuted={true}
                    />
                  </div>
                  {/* 可点击的透明层，用于跳转 - z-index低于视频控制按钮 */}
                  <Link
                    href={item?.button_url?.url || '#'}
                    className="absolute inset-0 z-10"
                    onClick={() => handleBannerClick(item)}
                    style={{ pointerEvents: 'auto' }}
                  />
                </>
              ) : (
                <Link
                  href={item?.button_url?.url || '#'}
                  className="block h-full"
                  onClick={() => handleBannerClick(item)}>
                  <Image
                    src={item?.image_url || ''}
                    alt={item?.title || 'Banner图片'}
                    height="100%"
                    width="100%"
                    lazy={index >= 2 ? true : false}
                    className="object-cover"
                  />
                </Link>
              )}

              {/* 内容区域 */}
              <div className="absolute bottom-[68px] left-0 right-0 z-20 flex flex-col items-center px-[16px] text-white">
                <div className="flex flex-col items-center justify-center">
                  {item?.subtitle && (
                    <p className="mb-[8px] line-clamp-1 text-lg leading-[22px]">{item?.subtitle}</p>
                  )}
                  {item?.title && (
                    <h2 className="mb-[8px] line-clamp-1 font-miSansDemiBold450 text-[36px] leading-[1.2]">
                      {item?.title}
                    </h2>
                  )}
                  {item?.description && (
                    <p className="line-clamp-2 text-center text-lg leading-[22px]">
                      {item?.description}
                    </p>
                  )}
                </div>

                {item?.button_text && (
                  <button
                    className="mt-[28px] flex h-[44px] w-[146px] items-center justify-center gap-2 rounded-full bg-primary px-11 py-base-12 text-lg text-white"
                    onClick={(e) => {
                      e.preventDefault()
                      e.stopPropagation()
                      handleDetailButtonClick(item, e)
                    }}>
                    {item?.button_text}
                    {item?.button_arrow ? <Arrow size={20} color="#fff" /> : ''}
                  </button>
                )}
              </div>
            </div>
          </Swiper.Item>
        ))}
      </Swiper>
    </div>
  )
}

export default Banner

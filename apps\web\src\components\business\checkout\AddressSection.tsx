'use client'
import { useEffect, useState } from 'react'
import { useSearchParams } from 'next/navigation'
import { useTranslations } from 'next-intl'
import {
  type Address,
  mergeStyles,
  setUserAllAddress,
  sleep,
  useDebounceFn,
  useToastContext,
  useUserAddress,
} from '@ninebot/core'
import { CustomerShippingAddressInput } from '@ninebot/core/src/graphql/generated/graphql'
import {
  checkoutSelectedShippingAddressSelector,
  setCheckoutPlaceOrderAddressId,
} from '@ninebot/core/src/store'
import { useAppDispatch, useAppSelector } from '@ninebot/core/src/store/hooks'
import { Button } from 'antd'

import { EmptyAddress, IconArrow, Location, Modal, Skeleton, Trash } from '@/components'

import AddressModal from './AddressModal'
import { Add } from './icons'

/**
 * 地址选择区域组件参数接口
 * @interface AddressSectionProps
 * @property {boolean} [isStandalone] - 是否是独立模式显示（不在结账流程中使用）
 * @property {boolean} isAddModalOpen - 添加地址模态框是否打开
 * @property {function} onAddModalClose - 关闭添加地址模态框的回调函数
 * @property {function} onAddModalOpen - 打开添加地址模态框的回调函数
 */
interface AddressSectionProps {
  isStandalone?: boolean
  isAddModalOpen: boolean
  onAddModalClose: () => void
  onAddModalOpen: () => void
}

/**
 * 地址选择与管理组件
 *
 * 提供用户地址的展示、选择、添加、编辑和删除功能
 * 支持两种显示模式：独立模式和结账流程模式
 *
 * @param {AddressSectionProps} props - 组件属性
 * @returns {JSX.Element} 地址选择区域组件
 */
export default function AddressSection({
  isStandalone = false,
  isAddModalOpen,
  onAddModalClose,
  onAddModalOpen,
}: AddressSectionProps) {
  const searchParams = useSearchParams()
  const toast = useToastContext()
  const dispatch = useAppDispatch()
  const getI18nString = useTranslations('Web')
  const {
    fetchUserAddresses,
    userAllAddress,
    updateUserAddress,
    deleteUserAddress,
    addUserAddress,
  } = useUserAddress()

  // 组件状态管理
  const [isLoading, setIsLoading] = useState(true) // 加载状态
  const [addresses, setAddresses] = useState<Address[]>([]) // 地址列表
  const [selectedAddress, setSelectedAddress] = useState<Address | null>(null) // 已选择的地址
  const [editingAddress, setEditingAddress] = useState<Address | null>(null) // 正在编辑的地址
  const [showAll, setShowAll] = useState(true) // 是否显示所有地址
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false) // 删除确认弹窗状态
  const [addressToDelete, setAddressToDelete] = useState<Address | null>(null) // 待删除的地址

  const checkoutSelectedShippingAddress = useAppSelector(checkoutSelectedShippingAddressSelector)

  /**
   * 请求所有地址数据
   * 组件挂载时获取用户所有地址
   */
  useEffect(() => {
    fetchUserAddresses().then(() => {
      setIsLoading(false)
    })
  }, [fetchUserAddresses])

  /**
   * 当全局状态中的地址列表和已选择的地址更新时，更新组件中的地址状态
   */
  useEffect(() => {
    setAddresses(userAllAddress)
    // 如果有已选择的地址，则设置为选中状态
    if (checkoutSelectedShippingAddress) {
      const selectedAddr = userAllAddress.find(
        (addr) => addr.address_id === checkoutSelectedShippingAddress.address_id,
      )
      if (selectedAddr) {
        setSelectedAddress(selectedAddr)
      }
    }
  }, [userAllAddress, checkoutSelectedShippingAddress])

  /**
   * 更新用户地址
   * 使用防抖函数避免频繁请求
   *
   * @param {Address} address - 要更新的地址对象
   * @param {boolean} isDefaultSet - 是否设为默认地址
   */
  const { run: handleUpdateUserAddress } = useDebounceFn(
    async (address: Address, isDefaultSet = false) => {
      if (address) {
        const result = await updateUserAddress(address as CustomerShippingAddressInput)
        if (result) {
          dispatch(setUserAllAddress(result))
          if (isDefaultSet) {
            await sleep(500)
            toast.show({
              icon: 'success',
              content: getI18nString('has_set_default_address'),
            })
          }
        }
      }
    },
  )

  /**
   * 删除用户地址
   * 使用防抖函数避免频繁请求
   *
   * @param {number} addressId - 要删除的地址ID
   */
  const { run: handleDeleteUserAddress } = useDebounceFn(async (addressId: number) => {
    if (addressId) {
      const result = await deleteUserAddress(addressId)
      if (result && Array.isArray(result)) {
        dispatch(setUserAllAddress(result))
        dispatch(setCheckoutPlaceOrderAddressId(''))
        await sleep(500)
        toast.show({
          icon: 'success',
          content: getI18nString('delete_address_success'),
        })
      }
    }
  })

  /**
   * 新增用户地址
   * 使用防抖函数避免频繁请求
   *
   * @param {Address} address - 要添加的地址对象
   */
  const { run: handleAddUserAddress } = useDebounceFn(async (address: Address) => {
    const result = await addUserAddress(address as CustomerShippingAddressInput)
    if (result) {
      toast.show({
        content: getI18nString('add_address_success'),
        icon: 'success',
      })

      // 全量更新地址数据
      dispatch(setUserAllAddress(result))

      if (JSON.parse(searchParams.get('isBack') || 'false')) {
        // 返回到 from 页面
        if (searchParams.get('back') === 'Checkout' && result.length) {
          // 新增地址后，取第 1 个地址ID
          dispatch(setCheckoutPlaceOrderAddressId(result?.[0]?.address_id))
        }
      }

      // 关闭添加地址模态框
      onAddModalClose()
    }
  })

  /**
   * 处理添加地址表单提交
   *
   * @param {Address} values - 新地址表单数据
   */
  const handleAddAddress = async (values: Address) => {
    await handleAddUserAddress(values)
    onAddModalClose()
  }

  /**
   * 处理编辑地址表单提交
   *
   * @param {Address} values - 编辑后的地址表单数据
   */
  const handleEditAddress = async (values: Address) => {
    if (!editingAddress) return

    setSelectedAddress((prev) =>
      prev?.address_id === editingAddress.address_id ? { ...editingAddress, ...values } : prev,
    )

    await handleUpdateUserAddress(values)

    setEditingAddress(null)
    onAddModalClose()
  }

  /**
   * 处理删除地址操作
   * 打开删除确认弹窗
   *
   * @param {Address} address - 要删除的地址对象
   */
  const handleDeleteAddress = (address: Address) => {
    setAddressToDelete(address)
    setIsDeleteModalOpen(true)
  }

  /**
   * 确认删除地址
   * 校验是否为默认地址，不能删除唯一的默认地址
   */
  const handleConfirmDelete = async () => {
    if (addressToDelete?.is_default) {
      toast.show({
        content: getI18nString('at_least_one_default_address'),
      })
      return
    }

    if (!addressToDelete) return

    await handleDeleteUserAddress(Number(addressToDelete.address_id))

    setIsDeleteModalOpen(false)
    setAddressToDelete(null)
  }

  /**
   * 选择地址
   *
   * @param {Address} address - 要选择的地址对象
   */
  const handleSelectAddress = (address: Address) => {
    // 更新组件内状态
    setSelectedAddress(address)

    // 更新 Redux store 中的选中地址
    dispatch(setCheckoutPlaceOrderAddressId(address.address_id))
  }

  /**
   * 打开编辑地址模态框
   *
   * @param {Address} address - 要编辑的地址对象
   */
  const openEditModal = (address: Address) => {
    setEditingAddress(address)
    onAddModalOpen()
  }

  /**
   * 设置默认地址
   * 使用防抖函数避免频繁请求，特别针对触摸设备优化
   * 在触摸设备上使用更长的防抖时间以提高精度
   *
   * @param {Address} address - 要设为默认的地址对象
   */
  const { run: handleSetDefault } = useDebounceFn(
    async (address: Address) => {
      // 立即更新本地状态，提供即时的UI反馈
      setAddresses((prevAddresses) =>
        prevAddresses.map((addr) => ({
          ...addr,
          is_default: addr.address_id === address.address_id,
        })),
      )

      setSelectedAddress((prev) =>
        prev?.address_id === address.address_id ? { ...prev, is_default: true } : prev,
      )

      // 调用API更新
      try {
        await handleUpdateUserAddress(
          {
            ...address,
            is_default: true,
          },
          true,
        )
      } catch {
        // 如果API调用失败，回滚本地状态
        setAddresses(userAllAddress)
        setSelectedAddress((prev) => {
          const originalAddr = userAllAddress.find((addr) => addr.address_id === prev?.address_id)
          return originalAddr || prev
        })
      }
    },
    {
      // 针对触摸设备优化，使用更长的防抖时间
      wait: typeof window !== 'undefined' && 'ontouchstart' in window ? 300 : 150,
    },
  )

  // 无地址时的显示逻辑
  if (addresses.length === 0 && isStandalone) {
    return (
      <div>
        <EmptyAddress />
        {/* 地址编辑/添加模态框 - 确保在无地址时也能显示 */}
        <AddressModal
          open={isAddModalOpen}
          editingAddress={editingAddress}
          onCancel={() => {
            onAddModalClose()
            setEditingAddress(null)
          }}
          onOk={editingAddress ? handleEditAddress : handleAddAddress}
        />
      </div>
    )
  }

  // 确定要显示的地址列表（全部或前3个）
  const displayedAddresses = showAll ? addresses : addresses.slice(0, 3)

  return (
    <div>
      {/* 标题和添加按钮区 - 只在非独立模式下显示 */}
      {!isStandalone && (
        <div className="mb-base-24 flex h-[36px] items-center justify-between">
          <h2 className="font-miSansDemiBold450 text-[20px] leading-[100%]">
            {getI18nString('shipping_address')}
          </h2>
          {addresses.length >= 3 && (
            <button
              className="flex items-center gap-x-[8px] font-miSansMedium380 text-[14px] leading-[1.4] text-black"
              onClick={onAddModalOpen}>
              <Add color="#000000" />
              {getI18nString('add_shipping_address')}
            </button>
          )}
        </div>
      )}

      {/* 地址列表区域 */}
      <div
        className={mergeStyles(
          'grid',
          isStandalone ? 'grid-cols-1' : 'grid-cols-2 gap-base-12 2xl:grid-cols-3',
        )}>
        {/* 加载状态 */}
        {isLoading ? (
          <>
            {!isStandalone
              ? Array.from({ length: 2 }).map((_, index) => (
                  <Skeleton
                    key={index}
                    style={{
                      borderRadius: 0,
                      width: '100%',
                      height: 81,
                      marginRight: 12,
                    }}
                  />
                ))
              : Array.from({ length: 3 }).map((_, index) => (
                  <div
                    key={index}
                    className={`flex flex-col gap-base py-8 ${index !== 2 ? 'border-b' : ''}`}>
                    {/* 顶部区域：姓名、电话和默认标签 */}
                    <div className="flex w-full items-center justify-between">
                      <div className="flex items-center gap-4">
                        <Skeleton style={{ width: 60, height: 24 }} />
                        <Skeleton style={{ width: 100, height: 24 }} />
                        <Skeleton style={{ width: 32, height: 18, borderRadius: 4 }} />
                      </div>
                      <Skeleton style={{ width: 28, height: 28, borderRadius: '50%' }} />
                    </div>
                    {/* 地址详情区域 */}
                    <Skeleton style={{ width: '80%', height: 16 }} />
                    {/* 底部操作按钮区域 */}
                    <div className="flex w-full flex-row items-center justify-end">
                      <div className="flex items-center gap-base-12">
                        <Skeleton style={{ width: 100, height: 24, borderRadius: 4 }} />
                        <Skeleton style={{ width: 80, height: 24, borderRadius: 4 }} />
                      </div>
                    </div>
                  </div>
                ))}
          </>
        ) : (
          <>
            {/* 地址卡片 - 根据不同模式显示不同样式 */}
            {displayedAddresses.map((address) =>
              !isStandalone ? (
                // 结账模式地址卡片
                <div
                  key={address.address_id}
                  className={`group relative h-[92px] w-full cursor-pointer items-center rounded-base px-base py-base-16 transition-colors ${
                    selectedAddress?.address_id === address.address_id
                      ? 'border-2 border-primary'
                      : 'border border-l-gray-200 focus:border-primary active:border-primary [@media(hover:hover)]:hover:border-primary'
                  }`}
                  onClick={() => handleSelectAddress(address)}>
                  <div className="flex gap-base-12">
                    <span className="h-[24px] w-[24px] flex-none">
                      <Location
                        fill={
                          selectedAddress?.address_id === address.address_id ? '#DA291C' : '#BBBBBD'
                        }
                      />
                    </span>
                    <div className="flex-1">
                      <div className="flex flex-nowrap items-center gap-[4px]">
                        <span className="line-clamp-1 break-all font-miSansDemiBold450 text-[14px] leading-[140%] text-[#0F0F0F]">
                          {address.receive_name}
                        </span>
                        <span className="font-miSansDemiBold450 text-[14px] leading-[140%] text-[#0F0F0F]">
                          {address.receive_phone}
                        </span>
                        {address.is_default && (
                          <span className="flex-none whitespace-nowrap rounded-[4px] bg-primary p-[4px] text-center font-miSansRegular330 text-[10px] leading-[100%] text-white">
                            {getI18nString('default_address')}
                          </span>
                        )}
                      </div>
                      <div className="mt-base line-clamp-2 break-all font-miSansRegular330 text-[12px] leading-[1.2] text-[#444446]">
                        {address.province} {address.city} {address.county} {address.street}
                      </div>
                    </div>
                  </div>

                  {/* 操作按钮组 - 桌面端悬停显示，移动端和平板始终显示 */}
                  <div className="absolute bottom-0 right-0 flex items-center gap-2 rounded-br-base rounded-tl-base bg-gray-base px-base py-[6px] [@media(hover:hover)_and_(min-width:1440px)]:hidden [@media(hover:hover)_and_(min-width:1440px)]:group-hover:flex">
                    {addresses.length > 1 && !address.is_default ? (
                      <>
                        <button
                          className="text-[12px] leading-none [@media(hover:hover)]:hover:text-primary"
                          onClick={(e) => {
                            e.stopPropagation()
                            handleDeleteAddress(address)
                          }}>
                          {getI18nString('delete_address')}
                        </button>
                        <span className="text-[12px] leading-none text-gray-3">|</span>
                      </>
                    ) : null}
                    <button
                      className="text-[12px] leading-none [@media(hover:hover)]:hover:text-primary"
                      onClick={(e) => {
                        e.stopPropagation()
                        openEditModal(address)
                      }}>
                      {getI18nString('edit_address')}
                    </button>
                  </div>
                </div>
              ) : (
                // 独立模式地址卡片
                <div
                  key={address.address_id}
                  className={`flex flex-col gap-base py-8 ${addresses.indexOf(address) !== addresses.length - 1 ? 'border-b' : ''}`}>
                  <div className="flex w-full items-center justify-between">
                    <div className="flex max-w-[90%] flex-nowrap items-center gap-4">
                      <span className="line-clamp-1 font-miSansDemiBold450 text-[20px] leading-[120%] tracking-[4%] text-[#0F0F0F]">
                        {address.receive_name}
                      </span>
                      <span className="font-miSansDemiBold450 text-[20px] leading-[120%] tracking-[4%] text-[#0F0F0F]">
                        {address.receive_phone}
                      </span>
                      {address.is_default && (
                        <div className="flex h-[18px] w-[32px] flex-none items-center justify-center rounded-[4px] bg-primary">
                          <span className="whitespace-nowrap font-miSansRegular330 text-[12px] leading-[100%] text-white">
                            {getI18nString('default_address')}
                          </span>
                        </div>
                      )}
                    </div>
                    <button
                      className="flex h-[28px] w-[28px] items-center justify-center"
                      onClick={(e) => {
                        e.stopPropagation()
                        handleDeleteAddress(address)
                      }}>
                      <Trash />
                    </button>
                  </div>
                  <div className="line-clamp-2 w-full font-miSansRegular330 text-[16px] leading-[1.2] text-[#444446]">
                    {address.province} {address.city} {address.county} {address.street}
                  </div>

                  {/* 地址操作按钮 */}
                  <div className="flex w-full flex-row items-center justify-end">
                    <div className="flex items-center gap-base-12">
                      {!address.is_default && (
                        <Button
                          type="text"
                          size="small"
                          className="address-btn"
                          onClick={(e) => {
                            e.stopPropagation()
                            handleSetDefault(address)
                          }}
                          style={{
                            borderRadius: 4,
                            border: '0.5px solid #D1D1D4',
                            padding: '1px 6px',
                            height: '24px',
                          }}>
                          <span className="font-miSansRegular330 text-[14px] leading-[22px] text-[#0F0F0F]">
                            {getI18nString('set_default_address')}
                          </span>
                        </Button>
                      )}

                      <Button
                        type="text"
                        size="small"
                        className="address-btn"
                        onClick={(e) => {
                          e.stopPropagation()
                          openEditModal(address)
                        }}
                        style={{
                          borderRadius: 4,
                          border: '0.5px solid #D1D1D4',
                          padding: '1px 6px',
                          height: '24px',
                        }}>
                        <span className="font-miSansRegular330 text-[14px] leading-[22px] text-[#0F0F0F]">
                          {getI18nString('edit_address')}
                        </span>
                      </Button>
                    </div>
                  </div>
                </div>
              ),
            )}

            {/* 添加地址卡片 - 在结账模式且地址不足3个时显示 */}
            {!isStandalone && addresses.length < 3 && (
              <div
                className="flex h-[92px] w-full cursor-pointer flex-col items-center justify-center rounded-lg border border-dashed border-gray-300 p-4 [@media(hover:hover)]:hover:border-red-500"
                style={{ touchAction: 'manipulation' }}
                onClick={(e) => {
                  e.preventDefault()
                  e.stopPropagation()
                  onAddModalOpen()
                }}>
                <div className="flex items-center gap-base">
                  <Add />
                  {getI18nString('add_shipping_address')}
                </div>
              </div>
            )}
          </>
        )}
      </div>

      {/* 展开/收起按钮 - 地址超过3个时显示 */}
      {!isStandalone && addresses.length > 3 && (
        <div className="mt-[24px] flex">
          <button onClick={() => setShowAll(!showAll)} className="flex items-center gap-1">
            {showAll ? getI18nString('collapse_all_address') : getI18nString('expand_all_address')}
            <IconArrow rotate={showAll ? 180 : 0} />
          </button>
        </div>
      )}

      {/* 地址编辑/添加模态框 */}
      <AddressModal
        open={isAddModalOpen}
        editingAddress={editingAddress}
        onCancel={() => {
          onAddModalClose()
          setEditingAddress(null)
        }}
        onOk={editingAddress ? handleEditAddress : handleAddAddress}
      />

      {/* 删除确认模态框 */}
      <Modal
        isOpen={isDeleteModalOpen}
        title={getI18nString('delete_address_title')}
        okText={getI18nString('confirm')}
        cancelText={getI18nString('thinking')}
        width={400}
        onConfirm={handleConfirmDelete}
        onClose={() => {
          setIsDeleteModalOpen(false)
          setAddressToDelete(null)
        }}>
        <p className="text-[18px]">{getI18nString('confirm_delete_address')}</p>
      </Modal>
    </div>
  )
}
